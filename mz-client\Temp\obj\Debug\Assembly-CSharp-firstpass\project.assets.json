{"version": 3, "targets": {".NETStandard,Version=v2.1": {"DG.Tweening/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/DG.Tweening.dll": {}}, "runtime": {"bin/placeholder/DG.Tweening.dll": {}}}, "DOTweenPro-Exclude/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"DG.Tweening": "1.0.0"}, "compile": {"bin/placeholder/DOTweenPro-Exclude.dll": {}}, "runtime": {"bin/placeholder/DOTweenPro-Exclude.dll": {}}}, "EPO/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPOUtilities": "1.0.0"}, "compile": {"bin/placeholder/EPO.dll": {}}, "runtime": {"bin/placeholder/EPO.dll": {}}}, "EPODemo/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPO": "1.0.0"}, "compile": {"bin/placeholder/EPODemo.dll": {}}, "runtime": {"bin/placeholder/EPODemo.dll": {}}}, "EPOEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPO": "1.0.0", "EPOHDRP": "1.0.0", "EPOURP": "1.0.0", "EPOUtilities": "1.0.0"}, "compile": {"bin/placeholder/EPOEditor.dll": {}}, "runtime": {"bin/placeholder/EPOEditor.dll": {}}}, "EPOHDRP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPO": "1.0.0"}, "compile": {"bin/placeholder/EPOHDRP.dll": {}}, "runtime": {"bin/placeholder/EPOHDRP.dll": {}}}, "EPOURP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPO": "1.0.0"}, "compile": {"bin/placeholder/EPOURP.dll": {}}, "runtime": {"bin/placeholder/EPOURP.dll": {}}}, "EPOUtilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/EPOUtilities.dll": {}}, "runtime": {"bin/placeholder/EPOUtilities.dll": {}}}, "FR2/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/FR2.dll": {}}, "runtime": {"bin/placeholder/FR2.dll": {}}}, "GameBase/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameProto": "1.0.0", "TEngine.Runtime": "1.0.0", "UniTask": "1.0.0"}, "compile": {"bin/placeholder/GameBase.dll": {}}, "runtime": {"bin/placeholder/GameBase.dll": {}}}, "GameCommon/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameBase": "1.0.0", "GameProto": "1.0.0", "TEngine.Runtime": "1.0.0"}, "compile": {"bin/placeholder/GameCommon.dll": {}}, "runtime": {"bin/placeholder/GameCommon.dll": {}}}, "GameLogic/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"DG.Tweening": "1.0.0", "GameBase": "1.0.0", "GameCommon": "1.0.0", "GameProto": "1.0.0", "JUTPS": "1.0.0", "PhysicsTankMaker": "1.0.0", "TEngine.Runtime": "1.0.0", "UniTask": "1.0.0", "YooAsset": "1.0.0"}, "compile": {"bin/placeholder/GameLogic.dll": {}}, "runtime": {"bin/placeholder/GameLogic.dll": {}}}, "GameProto/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/GameProto.dll": {}}, "runtime": {"bin/placeholder/GameProto.dll": {}}}, "JUTPS/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameCommon": "1.0.0"}, "compile": {"bin/placeholder/JUTPS.dll": {}}, "runtime": {"bin/placeholder/JUTPS.dll": {}}}, "JUTPS.CustomEditors/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"JUTPS": "1.0.0"}, "compile": {"bin/placeholder/JUTPS.CustomEditors.dll": {}}, "runtime": {"bin/placeholder/JUTPS.CustomEditors.dll": {}}}, "MagicaClothV2/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/MagicaClothV2.dll": {}}, "runtime": {"bin/placeholder/MagicaClothV2.dll": {}}}, "MagicaClothV2.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"MagicaClothV2": "1.0.0"}, "compile": {"bin/placeholder/MagicaClothV2.Editor.dll": {}}, "runtime": {"bin/placeholder/MagicaClothV2.Editor.dll": {}}}, "PhysicsTankMaker/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameCommon": "1.0.0", "TEngine.Runtime": "1.0.0"}, "compile": {"bin/placeholder/PhysicsTankMaker.dll": {}}, "runtime": {"bin/placeholder/PhysicsTankMaker.dll": {}}}, "PhysicsTankMaker-Editor-Exclude/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameCommon": "1.0.0", "PhysicsTankMaker": "1.0.0"}, "compile": {"bin/placeholder/PhysicsTankMaker-Editor-Exclude.dll": {}}, "runtime": {"bin/placeholder/PhysicsTankMaker-Editor-Exclude.dll": {}}}, "Physics_Track_System-exclude/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"PhysicsTankMaker": "1.0.0"}, "compile": {"bin/placeholder/Physics_Track_System-exclude.dll": {}}, "runtime": {"bin/placeholder/Physics_Track_System-exclude.dll": {}}}, "TEngine.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameLogic": "1.0.0", "TEngine.Runtime": "1.0.0", "YooAsset": "1.0.0", "YooAsset.Editor": "1.0.0"}, "compile": {"bin/placeholder/TEngine.Editor.dll": {}}, "runtime": {"bin/placeholder/TEngine.Editor.dll": {}}}, "TEngine.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0", "YooAsset": "1.0.0"}, "compile": {"bin/placeholder/TEngine.Runtime.dll": {}}, "runtime": {"bin/placeholder/TEngine.Runtime.dll": {}}}, "UniTask/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"YooAsset": "1.0.0"}, "compile": {"bin/placeholder/UniTask.dll": {}}, "runtime": {"bin/placeholder/UniTask.dll": {}}}, "UniTask.Addressables/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.Addressables.dll": {}}, "runtime": {"bin/placeholder/UniTask.Addressables.dll": {}}}, "UniTask.DOTween/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.DOTween.dll": {}}, "runtime": {"bin/placeholder/UniTask.DOTween.dll": {}}}, "UniTask.Linq/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.Linq.dll": {}}, "runtime": {"bin/placeholder/UniTask.Linq.dll": {}}}, "UniTask.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0"}, "compile": {"bin/placeholder/UniTask.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/UniTask.TextMeshPro.dll": {}}}, "YooAsset/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/YooAsset.dll": {}}, "runtime": {"bin/placeholder/YooAsset.dll": {}}}, "YooAsset.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"YooAsset": "1.0.0"}, "compile": {"bin/placeholder/YooAsset.Editor.dll": {}}, "runtime": {"bin/placeholder/YooAsset.Editor.dll": {}}}}}, "libraries": {"DG.Tweening/1.0.0": {"type": "project", "path": "DG.Tweening.csproj", "msbuildProject": "DG.Tweening.csproj"}, "DOTweenPro-Exclude/1.0.0": {"type": "project", "path": "DOTweenPro-Exclude.csproj", "msbuildProject": "DOTweenPro-Exclude.csproj"}, "EPO/1.0.0": {"type": "project", "path": "EPO.csproj", "msbuildProject": "EPO.csproj"}, "EPODemo/1.0.0": {"type": "project", "path": "EPODemo.csproj", "msbuildProject": "EPODemo.csproj"}, "EPOEditor/1.0.0": {"type": "project", "path": "EPOEditor.csproj", "msbuildProject": "EPOEditor.csproj"}, "EPOHDRP/1.0.0": {"type": "project", "path": "EPOHDRP.csproj", "msbuildProject": "EPOHDRP.csproj"}, "EPOURP/1.0.0": {"type": "project", "path": "EPOURP.csproj", "msbuildProject": "EPOURP.csproj"}, "EPOUtilities/1.0.0": {"type": "project", "path": "EPOUtilities.csproj", "msbuildProject": "EPOUtilities.csproj"}, "FR2/1.0.0": {"type": "project", "path": "FR2.csproj", "msbuildProject": "FR2.csproj"}, "GameBase/1.0.0": {"type": "project", "path": "GameBase.csproj", "msbuildProject": "GameBase.csproj"}, "GameCommon/1.0.0": {"type": "project", "path": "GameCommon.csproj", "msbuildProject": "GameCommon.csproj"}, "GameLogic/1.0.0": {"type": "project", "path": "GameLogic.csproj", "msbuildProject": "GameLogic.csproj"}, "GameProto/1.0.0": {"type": "project", "path": "GameProto.csproj", "msbuildProject": "GameProto.csproj"}, "JUTPS/1.0.0": {"type": "project", "path": "JUTPS.csproj", "msbuildProject": "JUTPS.csproj"}, "JUTPS.CustomEditors/1.0.0": {"type": "project", "path": "JUTPS.CustomEditors.csproj", "msbuildProject": "JUTPS.CustomEditors.csproj"}, "MagicaClothV2/1.0.0": {"type": "project", "path": "MagicaClothV2.csproj", "msbuildProject": "MagicaClothV2.csproj"}, "MagicaClothV2.Editor/1.0.0": {"type": "project", "path": "MagicaClothV2.Editor.csproj", "msbuildProject": "MagicaClothV2.Editor.csproj"}, "PhysicsTankMaker/1.0.0": {"type": "project", "path": "PhysicsTankMaker.csproj", "msbuildProject": "PhysicsTankMaker.csproj"}, "PhysicsTankMaker-Editor-Exclude/1.0.0": {"type": "project", "path": "PhysicsTankMaker-Editor-Exclude.csproj", "msbuildProject": "PhysicsTankMaker-Editor-Exclude.csproj"}, "Physics_Track_System-exclude/1.0.0": {"type": "project", "path": "Physics_Track_System-exclude.csproj", "msbuildProject": "Physics_Track_System-exclude.csproj"}, "TEngine.Editor/1.0.0": {"type": "project", "path": "TEngine.Editor.csproj", "msbuildProject": "TEngine.Editor.csproj"}, "TEngine.Runtime/1.0.0": {"type": "project", "path": "TEngine.Runtime.csproj", "msbuildProject": "TEngine.Runtime.csproj"}, "UniTask/1.0.0": {"type": "project", "path": "UniTask.csproj", "msbuildProject": "UniTask.csproj"}, "UniTask.Addressables/1.0.0": {"type": "project", "path": "UniTask.Addressables.csproj", "msbuildProject": "UniTask.Addressables.csproj"}, "UniTask.DOTween/1.0.0": {"type": "project", "path": "UniTask.DOTween.csproj", "msbuildProject": "UniTask.DOTween.csproj"}, "UniTask.Linq/1.0.0": {"type": "project", "path": "UniTask.Linq.csproj", "msbuildProject": "UniTask.Linq.csproj"}, "UniTask.TextMeshPro/1.0.0": {"type": "project", "path": "UniTask.TextMeshPro.csproj", "msbuildProject": "UniTask.TextMeshPro.csproj"}, "YooAsset/1.0.0": {"type": "project", "path": "YooAsset.csproj", "msbuildProject": "YooAsset.csproj"}, "YooAsset.Editor/1.0.0": {"type": "project", "path": "YooAsset.Editor.csproj", "msbuildProject": "YooAsset.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["DG.Tweening >= 1.0.0", "DOTweenPro-Exclude >= 1.0.0", "EPO >= 1.0.0", "EPODemo >= 1.0.0", "EPOEditor >= 1.0.0", "EPOHDRP >= 1.0.0", "EPOURP >= 1.0.0", "EPOUtilities >= 1.0.0", "FR2 >= 1.0.0", "GameBase >= 1.0.0", "GameCommon >= 1.0.0", "GameLogic >= 1.0.0", "GameProto >= 1.0.0", "JUTPS >= 1.0.0", "JUTPS.CustomEditors >= 1.0.0", "MagicaClothV2 >= 1.0.0", "MagicaClothV2.Editor >= 1.0.0", "PhysicsTankMaker >= 1.0.0", "PhysicsTankMaker-Editor-Exclude >= 1.0.0", "Physics_Track_System-exclude >= 1.0.0", "TEngine.Editor >= 1.0.0", "TEngine.Runtime >= 1.0.0", "UniTask >= 1.0.0", "UniTask.Addressables >= 1.0.0", "UniTask.DOTween >= 1.0.0", "UniTask.Linq >= 1.0.0", "UniTask.TextMeshPro >= 1.0.0", "YooAsset >= 1.0.0", "YooAsset.Editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}