﻿namespace EPOOutline
{
    /// <summary>
    /// Describes how the masking will behave for the object.
    /// </summary>
    public enum ComplexMaskingMode
    {
        /// <summary>
        /// No masking applied.
        /// </summary>
        None,
        /// <summary>
        /// Obstacles mode enabled.
        /// </summary>
        ObstaclesMode,
        /// <summary>
        /// Masking mode enabled.
        /// </summary>
        MaskingMode
    }
}