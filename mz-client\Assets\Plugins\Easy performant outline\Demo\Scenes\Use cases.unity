%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &1193395
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1193396}
  - component: {fileID: 1193399}
  - component: {fileID: 1193398}
  - component: {fileID: 1193397}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1193396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193395}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 402967733}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &1193397
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &1193398
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193395}
  m_Enabled: 1
--- !u!20 &1193399
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193395}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &61241494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 61241495}
  - component: {fileID: 61241498}
  - component: {fileID: 61241497}
  - component: {fileID: 61241496}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &61241495
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61241494}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1364486665}
  m_Father: {fileID: 508365269}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &61241496
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61241494}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &61241497
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61241494}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &61241498
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 61241494}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &127099143
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 127099147}
  - component: {fileID: 127099146}
  - component: {fileID: 127099145}
  - component: {fileID: 127099144}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &127099144
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127099143}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &127099145
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127099143}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5d9d040f497dc8418fbf3d58884e3fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &127099146
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127099143}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &127099147
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127099143}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.63, z: 9.5}
  m_LocalScale: {x: 20, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 219640926}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &152000302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 152000303}
  - component: {fileID: 152000305}
  - component: {fileID: 152000304}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &152000303
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152000302}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1664751283}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &152000304
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152000302}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Default rendering
--- !u!222 &152000305
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152000302}
  m_CullTransparentMesh: 0
--- !u!1 &169509896
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 169509897}
  - component: {fileID: 169509899}
  - component: {fileID: 169509898}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &169509897
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 169509896}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2043117538}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &169509898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 169509896}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Per object rendering (Performance heavy)
--- !u!222 &169509899
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 169509896}
  m_CullTransparentMesh: 0
--- !u!1 &193038950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 193038951}
  - component: {fileID: 193038954}
  - component: {fileID: 193038953}
  - component: {fileID: 193038952}
  m_Layer: 0
  m_Name: 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &193038951
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 193038950}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.75, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779837303}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &193038952
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 193038950}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 193038953}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 1
    blurShift: 0.185
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0.48426104, g: 0, b: 1, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &193038953
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 193038950}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &193038954
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 193038950}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &209605136
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 209605137}
  - component: {fileID: 209605140}
  - component: {fileID: 209605139}
  - component: {fileID: 209605138}
  m_Layer: 0
  m_Name: 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &209605137
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 209605136}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.75, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 508365269}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &209605138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 209605136}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 209605139}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 1
    blurShift: 0.185
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &209605139
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 209605136}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &209605140
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 209605136}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &219640925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 219640926}
  m_Layer: 0
  m_Name: Scene base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &219640926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 219640925}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1766318240}
  - {fileID: 1375459185}
  - {fileID: 127099147}
  - {fileID: 1332840417}
  - {fileID: 1093278023}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &256923741
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 256923742}
  - component: {fileID: 256923745}
  - component: {fileID: 256923744}
  - component: {fileID: 256923743}
  m_Layer: 0
  m_Name: 1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &256923742
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256923741}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.59, y: 0, z: 2.85}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 673373755}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &256923743
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256923741}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 256923744}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &256923744
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256923741}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &256923745
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256923741}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &273406901
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 273406902}
  - component: {fileID: 273406905}
  - component: {fileID: 273406904}
  - component: {fileID: 273406903}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &273406902
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 273406901}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.5, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779837303}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &273406903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 273406901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 273406904}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.67876756, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &273406904
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 273406901}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &273406905
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 273406901}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &297815421
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 297815422}
  m_Layer: 0
  m_Name: Masks
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &297815422
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 297815421}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 561403564}
  - {fileID: 1486538034}
  - {fileID: 544270501}
  - {fileID: 1785514956}
  - {fileID: 1690165553}
  - {fileID: 1163902290}
  - {fileID: 1046725612}
  m_Father: {fileID: 494821938}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &356385114
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 356385115}
  - component: {fileID: 356385118}
  - component: {fileID: 356385117}
  - component: {fileID: 356385116}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &356385115
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 356385114}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 673373755}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &356385116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 356385114}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 356385117}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 0.23921569}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.36862746}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &356385117
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 356385114}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &356385118
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 356385114}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &400217992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 400217993}
  - component: {fileID: 400217996}
  - component: {fileID: 400217995}
  - component: {fileID: 400217994}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &400217993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400217992}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 508365269}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &400217994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400217992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 400217995}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.01900088, b: 0, a: 1}
    dilateShift: 0.037
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &400217995
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400217992}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &400217996
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400217992}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &402967732
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402967733}
  m_Layer: 0
  m_Name: Front back mode with fills
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &402967733
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 402967732}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1193396}
  - {fileID: 2128736346}
  - {fileID: 1285051148}
  - {fileID: 1878451909}
  - {fileID: 1995084089}
  m_Father: {fileID: 494821938}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &416827468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 416827469}
  - component: {fileID: 416827472}
  - component: {fileID: 416827471}
  - component: {fileID: 416827470}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &416827469
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 416827468}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.66, y: 0, z: 1.35}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120039626}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &416827470
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 416827468}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 416827471}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &416827471
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 416827468}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &416827472
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 416827468}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &465974271
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 465974272}
  - component: {fileID: 465974275}
  - component: {fileID: 465974274}
  - component: {fileID: 465974273}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &465974272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465974271}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1619309732}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &465974273
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465974271}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &465974274
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465974271}
  m_Enabled: 1
--- !u!20 &465974275
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465974271}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &494821937
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 494821938}
  - component: {fileID: 494821939}
  m_Layer: 0
  m_Name: Cases
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &494821938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 494821937}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 508365269}
  - {fileID: 779837303}
  - {fileID: 1619309732}
  - {fileID: 402967733}
  - {fileID: 297815422}
  - {fileID: 1750367773}
  - {fileID: 673373755}
  - {fileID: 2120039626}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &494821939
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 494821937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8c7d530bcfbc51043bddc6d2df566e90, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &497031101
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 497031102}
  - component: {fileID: 497031105}
  - component: {fileID: 497031104}
  - component: {fileID: 497031103}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &497031102
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 497031101}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.25, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 508365269}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &497031103
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 497031101}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 497031104}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.67876756, b: 0, a: 1}
    dilateShift: 1
    blurShift: 0
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &497031104
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 497031101}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &497031105
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 497031101}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &508365268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 508365269}
  m_Layer: 0
  m_Name: Basic case
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &508365269
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 508365268}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1975802572}
  - {fileID: 61241495}
  - {fileID: 1671252720}
  - {fileID: 497031102}
  - {fileID: 400217993}
  - {fileID: 698202742}
  - {fileID: 209605137}
  m_Father: {fileID: 494821938}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &544270500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 544270501}
  - component: {fileID: 544270504}
  - component: {fileID: 544270503}
  - component: {fileID: 544270502}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &544270501
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 544270500}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 297815422}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &544270502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 544270500}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 2
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 544270503}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &544270503
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 544270500}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &544270504
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 544270500}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &561174897
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 561174898}
  - component: {fileID: 561174901}
  - component: {fileID: 561174900}
  - component: {fileID: 561174899}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &561174898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561174897}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120039626}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &561174899
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561174897}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 1
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &561174900
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561174897}
  m_Enabled: 1
--- !u!20 &561174901
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561174897}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &561403563
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 561403564}
  - component: {fileID: 561403567}
  - component: {fileID: 561403566}
  - component: {fileID: 561403565}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &561403564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561403563}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 297815422}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &561403565
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561403563}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &561403566
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561403563}
  m_Enabled: 1
--- !u!20 &561403567
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 561403563}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &646519843
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 646519844}
  - component: {fileID: 646519846}
  - component: {fileID: 646519845}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &646519844
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 646519843}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1428796797}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -60, y: -60}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &646519845
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 646519843}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 26
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 0
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Press right/left keyboard arrows to change usecases
--- !u!222 &646519846
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 646519843}
  m_CullTransparentMesh: 0
--- !u!1 &659026935
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 659026936}
  - component: {fileID: 659026939}
  - component: {fileID: 659026938}
  - component: {fileID: 659026937}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &659026936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659026935}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779837303}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &659026937
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659026935}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 659026938}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.01900088, b: 0, a: 1}
    dilateShift: 0.037
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 17986fbb77e96d643bf4c6da1860b9ef, type: 3}
      serializedProperties:
      - PropertyName: _PublicOuterColor
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.45490196}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicInnerColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicFresnelPower
        Property:
          ColorValue: {r: 0, g: 1, b: 0, a: 1}
          FloatValue: 2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicFresnelMultiplier
        Property:
          ColorValue: {r: 0, g: 1, b: 0, a: 1}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &659026938
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659026935}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &659026939
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659026935}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &664062759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 664062760}
  - component: {fileID: 664062763}
  - component: {fileID: 664062762}
  - component: {fileID: 664062761}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &664062760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664062759}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779837303}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &664062761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664062759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &664062762
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664062759}
  m_Enabled: 1
--- !u!20 &664062763
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664062759}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &673373754
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 673373755}
  m_Layer: 0
  m_Name: Default rendering strategy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &673373755
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 673373754}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1570375105}
  - {fileID: 1664751283}
  - {fileID: 1198159965}
  - {fileID: 1293041235}
  - {fileID: 356385115}
  - {fileID: 256923742}
  - {fileID: 1332717089}
  m_Father: {fileID: 494821938}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &696533386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 696533387}
  - component: {fileID: 696533389}
  - component: {fileID: 696533388}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &696533387
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696533386}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2133685129}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &696533388
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696533386}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Obstacles
--- !u!222 &696533389
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696533386}
  m_CullTransparentMesh: 0
--- !u!1 &698202741
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 698202742}
  - component: {fileID: 698202745}
  - component: {fileID: 698202744}
  - component: {fileID: 698202743}
  m_Layer: 0
  m_Name: 4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &698202742
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698202741}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.25, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 508365269}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &698202743
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698202741}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 698202744}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0.8993242, b: 1, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &698202744
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698202741}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &698202745
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698202741}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &714798918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 714798919}
  - component: {fileID: 714798922}
  - component: {fileID: 714798921}
  - component: {fileID: 714798920}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &714798919
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714798918}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1619309732}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &714798920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714798918}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 714798921}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &714798921
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714798918}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &714798922
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 714798918}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &737669073
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 737669074}
  - component: {fileID: 737669077}
  - component: {fileID: 737669076}
  - component: {fileID: 737669075}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &737669074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 737669073}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1619309732}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &737669075
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 737669073}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 737669076}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &737669076
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 737669073}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &737669077
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 737669073}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &768729426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 768729427}
  - component: {fileID: 768729429}
  - component: {fileID: 768729428}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &768729427
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 768729426}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1486538034}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &768729428
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 768729426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Mask
--- !u!222 &768729429
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 768729426}
  m_CullTransparentMesh: 0
--- !u!1 &773240176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 773240177}
  - component: {fileID: 773240180}
  - component: {fileID: 773240179}
  - component: {fileID: 773240178}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &773240177
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 773240176}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1750367773}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &773240178
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 773240176}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &773240179
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 773240176}
  m_Enabled: 1
--- !u!20 &773240180
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 773240176}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &779837302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 779837303}
  m_Layer: 0
  m_Name: Basic case with fills
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &779837303
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 779837302}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 664062760}
  - {fileID: 1592234600}
  - {fileID: 273406902}
  - {fileID: 1012163325}
  - {fileID: 659026936}
  - {fileID: 813597448}
  - {fileID: 193038951}
  m_Father: {fileID: 494821938}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &813597447
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 813597448}
  - component: {fileID: 813597451}
  - component: {fileID: 813597450}
  - component: {fileID: 813597449}
  m_Layer: 0
  m_Name: 4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &813597448
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 813597447}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.25, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779837303}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &813597449
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 813597447}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 813597450}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0.8993242, b: 1, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &813597450
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 813597447}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &813597451
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 813597447}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &821033879
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 821033880}
  - component: {fileID: 821033883}
  - component: {fileID: 821033882}
  - component: {fileID: 821033881}
  m_Layer: 0
  m_Name: 1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &821033880
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821033879}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.48, y: 0, z: 4.08}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120039626}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &821033881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821033879}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 821033882}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &821033882
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821033879}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &821033883
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821033879}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &850573693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 850573694}
  - component: {fileID: 850573697}
  - component: {fileID: 850573696}
  - component: {fileID: 850573695}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &850573694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 850573693}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1750367773}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &850573695
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 850573693}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 1
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 850573696}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 0.23921569}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.36862746}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &850573696
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 850573693}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &850573697
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 850573693}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &884790263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 884790264}
  - component: {fileID: 884790267}
  - component: {fileID: 884790266}
  - component: {fileID: 884790265}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &884790264
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 884790263}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120039626}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &884790265
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 884790263}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 884790266}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.8862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.16470589}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &884790266
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 884790263}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &884790267
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 884790263}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &910607631
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 910607632}
  - component: {fileID: 910607635}
  - component: {fileID: 910607634}
  - component: {fileID: 910607633}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &910607632
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910607631}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1619309732}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &910607633
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910607631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 910607634}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &910607634
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910607631}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &910607635
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910607631}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1012163324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1012163325}
  - component: {fileID: 1012163328}
  - component: {fileID: 1012163327}
  - component: {fileID: 1012163326}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1012163325
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1012163324}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.25, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 779837303}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1012163326
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1012163324}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1012163327}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.67876756, b: 0, a: 1}
    dilateShift: 1
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 77ea1b923175bd6439416078316b6264, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicHorizontalSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicVerticalSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicHorizontalGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0.5
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicVerticalGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0.5
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicVerticalSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicVerticalSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSecondaryAlpha
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &1012163327
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1012163324}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1012163328
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1012163324}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1046725611
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1046725612}
  - component: {fileID: 1046725615}
  - component: {fileID: 1046725614}
  - component: {fileID: 1046725613}
  m_Layer: 0
  m_Name: 3 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1046725612
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1046725611}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.54, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 297815422}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1046725613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1046725611}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 2
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1046725614}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 17986fbb77e96d643bf4c6da1860b9ef, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 0.23921569}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicVerticalSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicHorizontalGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.5
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicVerticalGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.5
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicVerticalSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicVerticalSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicSecondaryAlpha
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicOuterColor
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 1}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicInnerColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0, a: 1}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicFresnelPower
        Property:
          ColorValue: {r: 0, g: 1, b: 0, a: 1}
          FloatValue: 2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicFresnelMultiplier
        Property:
          ColorValue: {r: 0, g: 1, b: 0, a: 1}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.36862746}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicVerticalSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicHorizontalGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.5
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicVerticalGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.5
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicVerticalSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicHorizontalSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicVerticalSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicSecondaryAlpha
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1046725614
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1046725611}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1046725615
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1046725611}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1093278021
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1093278023}
  - component: {fileID: 1093278022}
  - component: {fileID: 1093278024}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1093278022
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093278021}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 0.576
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &1093278023
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093278021}
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 219640926}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &1093278024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1093278021}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 1
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
--- !u!1 &1118713698
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1118713699}
  - component: {fileID: 1118713701}
  - component: {fileID: 1118713700}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1118713699
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118713698}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1592234600}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1118713700
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118713698}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Basic outline with fills
--- !u!222 &1118713701
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118713698}
  m_CullTransparentMesh: 0
--- !u!1 &1163902289
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1163902290}
  - component: {fileID: 1163902294}
  - component: {fileID: 1163902293}
  - component: {fileID: 1163902292}
  - component: {fileID: 1163902291}
  m_Layer: 0
  m_Name: Mask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1163902290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1163902289}
  m_LocalRotation: {x: -0, y: -0, z: 0.70710576, w: 0.7071079}
  m_LocalPosition: {x: 0.4, y: 2.32, z: -3.35}
  m_LocalScale: {x: 0.79154, y: 16.904171, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 297815422}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90.00001}
--- !u!114 &1163902291
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1163902289}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 16
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1163902293}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!65 &1163902292
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1163902289}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1163902293
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1163902289}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5d9d040f497dc8418fbf3d58884e3fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1163902294
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1163902289}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1173205158
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1173205159}
  - component: {fileID: 1173205162}
  - component: {fileID: 1173205161}
  - component: {fileID: 1173205160}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1173205159
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1173205158}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1660840535}
  m_Father: {fileID: 1619309732}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1173205160
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1173205158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1173205161
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1173205158}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1173205162
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1173205158}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1196115882
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1196115883}
  - component: {fileID: 1196115886}
  - component: {fileID: 1196115885}
  - component: {fileID: 1196115884}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1196115883
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196115882}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1750367773}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1196115884
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196115882}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 1
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1196115885}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1196115885
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196115882}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1196115886
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196115882}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1198159964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1198159965}
  - component: {fileID: 1198159968}
  - component: {fileID: 1198159967}
  - component: {fileID: 1198159966}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1198159965
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198159964}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.66, y: 0, z: 1.35}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 673373755}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1198159966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198159964}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1198159967}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1198159967
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198159964}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1198159968
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198159964}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1224397230
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1224397232}
  - component: {fileID: 1224397231}
  m_Layer: 0
  m_Name: '!ftraceLightmaps'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1224397231
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1224397230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b7fa80e7116296f4eb4f49ec1544ee22, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &1224397232
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1224397230}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1285051147
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1285051148}
  - component: {fileID: 1285051151}
  - component: {fileID: 1285051150}
  - component: {fileID: 1285051149}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1285051148
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285051147}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 402967733}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1285051149
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285051147}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1285051150}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1285051150
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285051147}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1285051151
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285051147}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1293041234
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1293041235}
  - component: {fileID: 1293041238}
  - component: {fileID: 1293041237}
  - component: {fileID: 1293041236}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1293041235
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1293041234}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 673373755}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1293041236
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1293041234}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1293041237}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.8862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.16470589}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &1293041237
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1293041234}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1293041238
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1293041234}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1332717088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1332717089}
  - component: {fileID: 1332717092}
  - component: {fileID: 1332717091}
  - component: {fileID: 1332717090}
  m_Layer: 0
  m_Name: 1 (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1332717089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332717088}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.48, y: 0, z: 4.08}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 673373755}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1332717090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332717088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1332717091}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1332717091
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332717088}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1332717092
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332717088}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1332840413
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1332840417}
  - component: {fileID: 1332840416}
  - component: {fileID: 1332840415}
  - component: {fileID: 1332840414}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1332840414
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332840413}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1332840415
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332840413}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5d9d040f497dc8418fbf3d58884e3fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1332840416
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332840413}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1332840417
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332840413}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.63, z: -1.5}
  m_LocalScale: {x: 20, y: 1.7592, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 219640926}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1364486664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1364486665}
  - component: {fileID: 1364486667}
  - component: {fileID: 1364486666}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1364486665
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1364486664}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 61241495}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1364486666
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1364486664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Basic outline settings
--- !u!222 &1364486667
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1364486664}
  m_CullTransparentMesh: 0
--- !u!1 &1375459181
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1375459185}
  - component: {fileID: 1375459184}
  - component: {fileID: 1375459183}
  - component: {fileID: 1375459182}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1375459182
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1375459181}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1375459183
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1375459181}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5d9d040f497dc8418fbf3d58884e3fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1375459184
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1375459181}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1375459185
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1375459181}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.63, z: -9.5}
  m_LocalScale: {x: 20, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 219640926}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1415940855
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1415940858}
  - component: {fileID: 1415940857}
  - component: {fileID: 1415940856}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1415940856
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1415940855}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1415940857
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1415940855}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1415940858
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1415940855}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1428796793
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1428796797}
  - component: {fileID: 1428796796}
  - component: {fileID: 1428796795}
  - component: {fileID: 1428796794}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1428796794
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428796793}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1428796795
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428796793}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1428796796
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428796793}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1428796797
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428796793}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 646519844}
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &1431679536
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1431679537}
  - component: {fileID: 1431679539}
  - component: {fileID: 1431679538}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1431679537
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431679536}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2128736346}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1431679538
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431679536}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Basic outline with fills
--- !u!222 &1431679539
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431679536}
  m_CullTransparentMesh: 0
--- !u!1 &1486538033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1486538034}
  - component: {fileID: 1486538037}
  - component: {fileID: 1486538036}
  - component: {fileID: 1486538035}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1486538034
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486538033}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 768729427}
  m_Father: {fileID: 297815422}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1486538035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486538033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1486538036
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486538033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1486538037
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486538033}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1570375104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1570375105}
  - component: {fileID: 1570375108}
  - component: {fileID: 1570375107}
  - component: {fileID: 1570375106}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1570375105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570375104}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 673373755}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &1570375106
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570375104}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!81 &1570375107
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570375104}
  m_Enabled: 1
--- !u!20 &1570375108
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570375104}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &1592234599
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1592234600}
  - component: {fileID: 1592234603}
  - component: {fileID: 1592234602}
  - component: {fileID: 1592234601}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1592234600
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1592234599}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1118713699}
  m_Father: {fileID: 779837303}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1592234601
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1592234599}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1592234602
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1592234599}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1592234603
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1592234599}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1619309731
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1619309732}
  m_Layer: 0
  m_Name: Front back mode
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1619309732
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619309731}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 465974272}
  - {fileID: 1173205159}
  - {fileID: 737669074}
  - {fileID: 714798919}
  - {fileID: 910607632}
  m_Father: {fileID: 494821938}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1650757132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1650757133}
  - component: {fileID: 1650757136}
  - component: {fileID: 1650757135}
  - component: {fileID: 1650757134}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1650757133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650757132}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1750367773}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1650757134
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650757132}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 1
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1650757135}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.8862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.16470589}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &1650757135
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650757132}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1650757136
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650757132}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1660840534
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1660840535}
  - component: {fileID: 1660840537}
  - component: {fileID: 1660840536}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1660840535
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660840534}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1173205159}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1660840536
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660840534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 71
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 71
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Front back outline
--- !u!222 &1660840537
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660840534}
  m_CullTransparentMesh: 0
--- !u!1 &1664751282
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1664751283}
  - component: {fileID: 1664751286}
  - component: {fileID: 1664751285}
  - component: {fileID: 1664751284}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1664751283
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664751282}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 152000303}
  m_Father: {fileID: 673373755}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1664751284
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664751282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1664751285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664751282}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1664751286
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664751282}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1671252719
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1671252720}
  - component: {fileID: 1671252723}
  - component: {fileID: 1671252722}
  - component: {fileID: 1671252721}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1671252720
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1671252719}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.5, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 508365269}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1671252721
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1671252719}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1671252722}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.67876756, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &1671252722
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1671252719}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1671252723
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1671252719}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1690165552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1690165553}
  - component: {fileID: 1690165556}
  - component: {fileID: 1690165555}
  - component: {fileID: 1690165554}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1690165553
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690165552}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 297815422}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1690165554
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690165552}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 2
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1690165555}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 0.23921569}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.36862746}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1690165555
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690165552}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1690165556
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690165552}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1750367772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1750367773}
  m_Layer: 0
  m_Name: Obstacles
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1750367773
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1750367772}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 773240177}
  - {fileID: 2133685129}
  - {fileID: 1196115883}
  - {fileID: 1650757133}
  - {fileID: 850573694}
  - {fileID: 1974621328}
  m_Father: {fileID: 494821938}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1766318236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1766318240}
  - component: {fileID: 1766318239}
  - component: {fileID: 1766318238}
  - component: {fileID: 1766318237}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1766318237
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766318236}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1766318238
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766318236}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5d9d040f497dc8418fbf3d58884e3fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1766318239
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766318236}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1766318240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766318236}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -1.38, z: 0}
  m_LocalScale: {x: 20, y: 1, z: 20}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 219640926}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1785514955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1785514956}
  - component: {fileID: 1785514959}
  - component: {fileID: 1785514958}
  - component: {fileID: 1785514957}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1785514956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1785514955}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 297815422}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1785514957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1785514955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 2
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1785514958}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.8862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.16470589}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &1785514958
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1785514955}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1785514959
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1785514955}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1878451908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1878451909}
  - component: {fileID: 1878451912}
  - component: {fileID: 1878451911}
  - component: {fileID: 1878451910}
  m_Layer: 0
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1878451909
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1878451908}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 402967733}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1878451910
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1878451908}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1878451911}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.8862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.45385426, b: 0, a: 0.16470589}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!23 &1878451911
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1878451908}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1878451912
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1878451908}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1974621327
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1974621328}
  - component: {fileID: 1974621332}
  - component: {fileID: 1974621331}
  - component: {fileID: 1974621330}
  - component: {fileID: 1974621329}
  m_Layer: 0
  m_Name: Mask
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1974621328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974621327}
  m_LocalRotation: {x: -0, y: -0, z: 0.70710576, w: 0.7071079}
  m_LocalPosition: {x: 0.4, y: 2.33, z: -3.35}
  m_LocalScale: {x: 0.79154, y: 16.904171, z: 0.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1750367773}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90.00001}
--- !u!114 &1974621329
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974621327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 8
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1974621331}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 1
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!65 &1974621330
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974621327}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1974621331
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974621327}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5d9d040f497dc8418fbf3d58884e3fd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1974621332
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974621327}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1975802569
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1975802572}
  - component: {fileID: 1975802571}
  - component: {fileID: 1975802570}
  - component: {fileID: 1975802573}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1975802570
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1975802569}
  m_Enabled: 1
--- !u!20 &1975802571
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1975802569}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1975802572
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1975802569}
  m_LocalRotation: {x: 0.3656389, y: -0, z: -0, w: 0.9307568}
  m_LocalPosition: {x: 0, y: 5.4, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 508365269}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 42.894, y: 0, z: 0}
--- !u!114 &1975802573
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1975802569}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7bb42831ee5422f409e7f24f9e7488ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stage: 1
  renderingStrategy: 0
  renderingMode: 0
  outlineLayerMask: -1
  primaryBufferSizeMode: 3
  primaryRendererScale: 1
  primarySizeReference: 800
  blurShift: 1
  dilateShift: 1
  dilateIterations: 3
  dilateQuality: 0
  blurIterations: 3
  blurType: 1
--- !u!1 &1995084088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1995084089}
  - component: {fileID: 1995084092}
  - component: {fileID: 1995084091}
  - component: {fileID: 1995084090}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1995084089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995084088}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 402967733}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1995084090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995084088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1995084091}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 0.23921569}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.36862746}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1995084091
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995084088}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1995084092
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995084088}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1995355132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1995355133}
  - component: {fileID: 1995355136}
  - component: {fileID: 1995355135}
  - component: {fileID: 1995355134}
  m_Layer: 0
  m_Name: 1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1995355133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995355132}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.59, y: 0, z: 2.85}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120039626}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1995355134
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995355132}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 1995355135}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 0
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.3254902}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &1995355135
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995355132}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1995355136
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995355132}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2043117537
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2043117538}
  - component: {fileID: 2043117541}
  - component: {fileID: 2043117540}
  - component: {fileID: 2043117539}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2043117538
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043117537}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 169509897}
  m_Father: {fileID: 2120039626}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2043117539
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043117537}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &2043117540
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043117537}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &2043117541
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2043117537}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &2113882830
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2113882831}
  - component: {fileID: 2113882834}
  - component: {fileID: 2113882833}
  - component: {fileID: 2113882832}
  m_Layer: 0
  m_Name: 3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2113882831
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113882830}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2120039626}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2113882832
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113882830}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 2113882833}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 0, g: 0, b: 0, a: 1}
    dilateShift: 0.37
    blurShift: 0
    fillPass:
      shader: {fileID: 4800000, guid: 213090e563021044c92d1c27258b93f0, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 0, g: 1, b: 0.5332718, a: 0.6862745}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
  backParameters:
    enabled: 1
    color: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.015686274, b: 0.06288016, a: 0.23921569}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
  frontParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 4800000, guid: 4b218962224bcd449ba16572d6a50cee, type: 3}
      serializedProperties:
      - PropertyName: _PublicColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicGapColor
        Property:
          ColorValue: {r: 1, g: 0.92156863, b: 0.015686275, a: 0.36862746}
          FloatValue: 0
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 0
      - PropertyName: _PublicSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicGapSize
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.2
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSoftness
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 0.75
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
      - PropertyName: _PublicSpeed
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 1
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 2
      - PropertyName: _PublicAngle
        Property:
          ColorValue: {r: 1, g: 0, b: 0, a: 0.2}
          FloatValue: 45
          VectorValue: {x: 0, y: 0, z: 0, w: 0}
          PropertyType: 3
--- !u!23 &2113882833
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113882830}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b60e4ec1a9706a442a76bb9a56c4452e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2113882834
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113882830}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2120039625
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2120039626}
  m_Layer: 0
  m_Name: Per object rendering strategy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2120039626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2120039625}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 561174898}
  - {fileID: 2043117538}
  - {fileID: 416827469}
  - {fileID: 884790264}
  - {fileID: 2113882831}
  - {fileID: 1995355133}
  - {fileID: 821033880}
  m_Father: {fileID: 494821938}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2128736345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2128736346}
  - component: {fileID: 2128736349}
  - component: {fileID: 2128736348}
  - component: {fileID: 2128736347}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2128736346
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2128736345}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1431679537}
  m_Father: {fileID: 402967733}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2128736347
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2128736345}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &2128736348
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2128736345}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &2128736349
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2128736345}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &2133685128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2133685129}
  - component: {fileID: 2133685132}
  - component: {fileID: 2133685131}
  - component: {fileID: 2133685130}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2133685129
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2133685128}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -4.49}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 696533387}
  m_Father: {fileID: 1750367773}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: -0.63}
  m_SizeDelta: {x: 1058, y: 590}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2133685130
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2133685128}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &2133685131
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2133685128}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &2133685132
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2133685128}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
