{"name": "EPOEditor", "rootNamespace": "", "references": ["Unity.RenderPipelines.HighDefinition.Runtime", "Unity.RenderPipelines.HighDefinition.Config.Runtime", "Unity.RenderPipelines.Universal.Runtime", "EPO", "EPOUtilities", "EPOURP", "EPOHDRP"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.render-pipelines.high-definition", "expression": "0.0.0", "define": "HDRP_OUTLINE"}, {"name": "com.unity.render-pipelines.universal", "expression": "0.0.0", "define": "URP_OUTLINE"}], "noEngineReferences": false}